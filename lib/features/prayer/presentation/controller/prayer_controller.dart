import 'dart:async';
import 'dart:io';

import 'package:adhan/adhan.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:home_widget/home_widget.dart';
import 'package:logger/logger.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_notifications.dart';
import 'package:salawati/features/location/data/models/location_model.dart';
import 'package:salawati/features/location/data/providers/locations_database_provider.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/prayer/presentation/widgets/new_widget.dart';
import 'package:salawati/features/settings/data/models/adhan_model.dart';
import 'package:salawati/features/settings/data/models/calculation_method_model.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

class PrayerController extends GetxController {
  static PrayerController get instance => Get.find();

  // Dependencies
  final Logger _logger = Logger(); // For logging

  // Reactive state
  final prayerDatePage = 0.obs;
  final prayerTimes = Rxn<PrayerTimes>(); // Reactive prayer times
  final currentCity = Rxn<String>(); // Reactive current city
  final qiblahDegree = Rxn<double>(); // Reactive Qiblah degree
  final currentCountry = Rxn<String>();
  final sunnahTimes = Rxn<SunnahTimes>();

  // Constants
  static const int initialPrayerPage = 500;
  static const double kaabaLatitude = 21.4225;
  static const double kaabaLongitude = 39.8262;

  // Controller state
  CalculationParameters params;
  Position? position;
  double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
  double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
  late Coordinates coordinates = Coordinates(latitude, longitude);
  late DateComponents dateComponents;

  final prayersPageController = PageController(initialPage: initialPrayerPage);

  PrayerController() : params = CalculationMethod.umm_al_qura.getParameters();
  final LocationService locationService = Get.find();
  // bool get hasValidLocation => isValidCoordinate(latitude, longitude);
  final Rx<Duration> remainingTime = Duration.zero.obs;
  Timer? _timer;
  // Timer? _widgetUpdateTimer;

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (prayerTimes.value != null) {
        _updateRemainingTime();
      }
    });
    if (prayerTimes.value != null) {
      _updateRemainingTime();
    }
  }

  void stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  void _updateRemainingTime() {
    // debugPrint(
    //     "Updating remaining time: ${remainingTime.value} at ${DateTime.now().second}:${DateTime.now().millisecond}");
    if (prayerTimes.value != null) {
      remainingTime.value = AppFunctions.nextPrayerRemainingTime(
        prayerTimes.value!,
      );
      update(); // Force rebuild for GetBuilder listeners
    }
  }

  @override
  void onClose() {
    // To stop timer when controller is disposed
    stopTimer();
    // _stopWidgetUpdateTimer();

    super.onClose();
  }

  @override
  Future<void> onInit() async {
    await init();
    setupLocationListeners();
    startTimer();
    // _startWidgetUpdateTimer();

    super.onInit();
  }

  // void _startWidgetUpdateTimer() {
  //   _widgetUpdateTimer =
  //       Timer.periodic(const Duration(minutes: 1), (timer) async {
  //     await Get.find<HomeWidgetController>().onInit();

  //     debugPrint('Widget update triggered by timer');
  //   });
  // }

  // void _stopWidgetUpdateTimer() {
  //   _widgetUpdateTimer?.cancel();
  //   _widgetUpdateTimer = null;
  // }

  Future<void> init() async {
    try {
      final bool isManualLocation =
          cacheMemory.read(USER_SET_LOCATION) ?? false;
      final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;

      if (isManualLocation) {
        // Use manual coordinates without any service checks
        latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
        longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
        coordinates = Coordinates(latitude, longitude);
        currentCity.value = cacheMemory.read(CITY) ?? 'Makkah'.tr;
        currentCountry.value = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia'.tr;
      } else {
        if (isLocationAuto) {
          await getCustomLocation();
        } else {
          // Use default coordinates without services
          await fallbackToDefaultLocation();
        }
      }

      await getCalculationMethodParams();
      await calculatePrayerTimes();
      await calculateQiblahDegree();
      await checkLocationRequirements();
    } catch (e) {
      _logger.e('Init error: $e');
      debugPrint('amin fallbackToDefaultLocation 7');

      await fallbackToDefaultLocation();
    }
  }

  Future<void> checkLocationRequirements() async {
    final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
    if (isManualLocation) return;

    // if (!isManualLocation && !locationController.hasLocationAccess) {
    //   // await Future.delayed(const Duration(microseconds: 200));
    //   if (!locationController.locationDialogShown.value) {
    //     await locationController.showLocationRequirementDialog();
    //   }
    // }
  }

  void setupLocationListeners() {
    final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;

    if (isManualLocation == false) {
      // Monitor location service and permission status
      ever(locationService.serviceEnabled, (_) => _checkLocationStatus());
      ever(locationService.permissionStatus, (_) => _checkLocationStatus());
    }
  }

  void _checkLocationStatus() {
    if (locationService.hasLocationAccess) {
      calculatePrayerTimes();
    } else {
      fallbackToDefaultLocation();
    }

    checkLocationRequirements();
  }

  Future<void> updateSalatWidget() async {
    if (Get.isRegistered<PrayerDataService>()) {
      try {
        await PrayerDataService.instance.refreshPrayerData();
        await PrayerDataService.instance.sendToWidget();
        debugPrint('Widget, is Synced in background on callbackDispatcher ');
      } catch (e) {
        debugPrint('Widget on callbackDispatcher group amin $e');
      }
    }
  }

  Future<void> getCustomLocation() async {
    // debugPrint('amin getCustomLocation');
    try {
      final bool isManualLocation =
          cacheMemory.read(USER_SET_LOCATION) ?? false;
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
      // debugPrint('amin isManualLocation = $isManualLocation');
      if (isManualLocation) {
        debugPrint('amin Skipping GPS fetch - manual location is set');
        return;
      }
      Position? position = await locationService.getCurrentPosition();
      if (position == null) {
        throw LocationFetchException('Failed to get current position');
      }
      if (!isValidCoordinate(position.latitude, position.longitude)) {
        throw InvalidCoordinatesException(
            position.latitude, position.longitude);
      }

      debugPrint('amin get postion');

      latitude = position.latitude;
      longitude = position.longitude;
      debugPrint('amin get postion $latitude, $longitude');

      await cacheMemory.write(LATITUDE, latitude);
      await cacheMemory.write(LONGITUDE, longitude);
      await cacheMemory.write(IS_LOCATION_AUTO, true);

      await getCurrentCityUsingGeocode();
      await calculateQiblahDegree();
      await calculatePrayerTimes();

      update();
      if (Platform.isIOS) {
        await updateSalatWidget();
      }
    } catch (e) {
      _logger.e('Error getting custom location: $e');
      await _handleLocationError(e);
    }
  }

  Future<void> _handleLocationError(dynamic error) async {
    if (error is LocationServiceDisabledException) {
      _logger.w('Location services disabled - using cached data');
    } else if (error is PermissionDeniedException) {
      _logger.w('Location permissions denied - using cached data');
    }

    // Use last valid coordinates or fallback to Kaaba
    final lastLat = cacheMemory.read(LATITUDE);
    final lastLng = cacheMemory.read(LONGITUDE);

    if (lastLat != null &&
        lastLng != null &&
        isValidCoordinate(lastLat, lastLng)) {
      coordinates = Coordinates(lastLat, lastLng);
    } else {
      await fallbackToDefaultLocation();
    }

    await calculatePrayerTimes();
    update();
  }

  bool isValidCoordinate(double lat, double lng) {
    return lat.abs() > 0.1 && lng.abs() > 0.1;
  }

  void handleLocationError(dynamic error) {
    debugPrint('amin handleLocationError');
    final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;

    if (isManualLocation) {
      // Only show errors if user has manual location set
      String errorMessage;
      if (error.toString().contains('permanently denied')) {
        errorMessage = 'Location permission permanently denied. Please enable in settings.'.tr;
      } else if (error.toString().contains('permission')) {
        errorMessage = 'Location permission denied.'.tr;
      } else {
        errorMessage = error.toString();
      }

      Get.snackbar(
        'Location Error'.tr,
        errorMessage,
        duration: const Duration(seconds: 3),
      );
    }
    debugPrint('amin fallbackToDefaultLocation 8');

    fallbackToDefaultLocation();
  }

  Future<void> fallbackToDefaultLocation() async {
    double lat = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
    double lng = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;

    // Validate with more precise checks
    bool isValid = lat >= -90 &&
        lat <= 90 &&
        lng >= -180 &&
        lng <= 180 &&
        (lat != KAABA_LATITUDE || lng != KAABA_LONGITUDE);

    if (!isValid) {
      lat = KAABA_LATITUDE;
      lng = KAABA_LONGITUDE;
      await cacheMemory.write(LATITUDE, lat);
      await cacheMemory.write(LONGITUDE, lng);
    }

    coordinates = Coordinates(lat, lng);
    currentCity.value = cacheMemory.read(CITY) ?? 'Makkah'.tr;
    currentCountry.value = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia'.tr;
  }
  // Future<void> fallbackToDefaultLocation() async {
  //   if (cacheMemory.read(USER_SET_LOCATION) == true) return;

  //   // Retrieve cached values or use Kaaba as fallback
  //   double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
  //   double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
  //   currentCity.value = cacheMemory.read(CITY) ?? 'Makkah'.tr;
  //   currentCountry.value = cacheMemory.read(COUNTRY) ?? 'Saudi Arabia'.tr;

  //   // Validate coordinates
  //   if (!isValidCoordinate(latitude, longitude)) {
  //     // Reset to Kaaba if invalid
  //     latitude = KAABA_LATITUDE;
  //     longitude = KAABA_LONGITUDE;
  //     currentCity.value = 'Makkah'.tr;
  //     currentCountry.value = 'Saudi Arabia'.tr;
  //   }

  //   // Update cache with valid coordinates
  //   await cacheMemory.write(LATITUDE, latitude);
  //   await cacheMemory.write(LONGITUDE, longitude);
  //   await cacheMemory.write(CITY, currentCity.value);
  //   await cacheMemory.write(COUNTRY, currentCountry.value);

  //   coordinates = Coordinates(latitude, longitude);
  //   await calculatePrayerTimes();
  //   await calculateQiblahDegree();
  //   update();
  // }

  Future<void> getCurrentCityUsingGeocode() async {
    debugPrint('amin start getCurrentCityUsingGeocode');
    final bool isManualLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
    final bool isLocationAuto = cacheMemory.read(IS_LOCATION_AUTO) ?? false;

    double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
    double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
    if (isManualLocation) {
      _logger.i(
          'Skipping GPS fetch1 - manual location is set $latitude $longitude');

      return;
    }
    if (isLocationAuto == false) {
      _logger.i(
          'Skipping GPS fetch3 - manual location is set $latitude $longitude');

      return;
    }

    try {
      debugPrint(
          'amin getCurrentCityUsingGeocode1 IS_LOCATION_AUTO ${cacheMemory.read(IS_LOCATION_AUTO)} ');

      await setLocaleIdentifier(cacheMemory.read('lang') ?? 'ar');

      if (!(await Connectivity().checkConnectivity())
          .contains(ConnectivityResult.none)) {
        List<Placemark> placemarks =
            await placemarkFromCoordinates(latitude, longitude);

        if (placemarks.isEmpty) {
          _logger.w('No placemarks found');
          await _getCityViaNominatim();
          return;
        }

        Placemark place = placemarks.first;

        if (Platform.isIOS) {
          currentCity.value = place.name ??
              place.subLocality ??
              place.locality ??
              place.subAdministrativeArea ??
              place.administrativeArea ??
              'Unknown';
          currentCountry.value = place.country ?? 'Unknown';
        } else {
          currentCity.value = place.administrativeArea ??
              place.locality ??
              place.subLocality ??
              place.subAdministrativeArea ??
              'Unknown';
          currentCountry.value = place.country ?? 'Unknown';
        }
        debugPrint(
            'amin lat5 $latitude long:$longitude city: ${currentCity.value}');

        await cacheMemory.write(CITY, currentCity.value);
        await cacheMemory.write(COUNTRY, currentCountry.value);

        await cacheMemory.write(IS_LOCATION_AUTO, true);
        Get.find<SettingsController>().update();

        await HomeWidget.saveWidgetData<String>('city_name', currentCity.value);
        await HomeWidget.saveWidgetData<double>('longitude', longitude);
        await HomeWidget.saveWidgetData<double>('latitude', latitude);
      } else {
        await _getCityViaNominatim();
      }

      await HomeWidget.saveWidgetData<String>('city_name', currentCity.value);
      await HomeWidget.saveWidgetData<double>('longitude', longitude);
      await HomeWidget.saveWidgetData<double>('latitude', latitude);

      await cacheMemory.write(IS_LOCATION_AUTO, true);
      Get.find<SettingsController>().isLocationAuto.value = true;
    } catch (e) {
      debugPrint(
          'amin getCurrentCityUsingGeocode3 IS_LOCATION_AUTO ${cacheMemory.read(IS_LOCATION_AUTO)} catch ');

      final userSetLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
      currentCity.value =
          userSetLocation ? cacheMemory.read(CITY) : 'Makkah'.tr;

      currentCountry.value =
          userSetLocation ? cacheMemory.read(COUNTRY) : 'Saudi Arabia'.tr;
      await cacheMemory.write(CITY, currentCity.value);
      await cacheMemory.write(COUNTRY, currentCountry.value);
    }
  }

  Future<void> _getCityViaNominatim() async {
    try {
      debugPrint('aminusing offline datebase LocationsDatabaseProvider ');
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;

      var city =
          await LocationsDatabaseProvider.getNearestCity(longitude, latitude);
      currentCity.value = city?.name ?? 'Unknown';

      currentCountry.value = city?.countryName ?? 'Unknown';
      update();
    } catch (nominatimError) {
      final userSetLocation = cacheMemory.read(USER_SET_LOCATION) ?? false;
      currentCity.value =
          userSetLocation ? cacheMemory.read(CITY) : 'Makkah'.tr;
      currentCountry.value =
          userSetLocation ? cacheMemory.read(COUNTRY) : 'Saudi Arabia'.tr;
      await cacheMemory.write(CITY, currentCity.value);
      await cacheMemory.write(COUNTRY, currentCountry.value);
    }
  }

  Future<void> calculatePrayerTimes() async {
    try {
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
      if (!isValidCoordinate(latitude, longitude)) {
        _logger.e('Invalid coordinates for prayer calculation');
        debugPrint('amin fallbackToDefaultLocation 9 ');

        await fallbackToDefaultLocation();
        return;
      }
      coordinates = Coordinates(latitude, longitude);
      await getCalculationMethodParams();
      await getMadhab();
      await getHighLatitudeRule();
      await getAdjustments();
      await getDateTime();
      await justCalculateTimes();
      update();
    } catch (e) {
      _logger.e('Error calculating prayer times: $e');
    }
  }

  Future<void> justCalculateTimes({DateComponents? date}) async {
    try {
      prayerTimes.value = PrayerTimes.utcOffset(
        coordinates,
        date ?? dateComponents,
        params,
        getUtcOffset(),
      );
      if (prayerTimes.value != null) {
        sunnahTimes.value = SunnahTimes(prayerTimes.value!);
      }
      update();
    } catch (e) {
      _logger.e('Error calculating times: $e');
    }
  }

  Future<void> getDateTime() async {
    try {
      DateTime currentDateTime =
          DateTime.now().add(Duration(days: prayerDatePage.value));
      dateComponents = DateComponents(
        currentDateTime.year,
        currentDateTime.month,
        currentDateTime.day,
      );
      update();
    } catch (e) {
      _logger.e('Error getting date: $e');
    }
  }

  Future<void> calculateQiblahDegree() async {
    try {
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
      Coordinates coordinates = Coordinates(latitude, longitude);
      final qibla = Qibla(coordinates);
      qiblahDegree.value = qibla.direction;
    } catch (e) {
      _logger.e('Error calculating Qiblah degree: $e');
    }
    update();
  }

  double? getQiblahDegree() {
    try {
      double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
      double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
      Coordinates coordinates = Coordinates(latitude, longitude);
      final qibla = Qibla(coordinates);
      qiblahDegree.value = qibla.direction;
      return qiblahDegree.value;
    } catch (e) {
      _logger.e('Error calculating Qiblah degree: $e');
    }
    update();
    return null;
  }

  Future<void> getCalculationMethodParams() async {
    try {
      if (cacheMemory.read(IS_CALCULATION_METHOD_AUTO) != false) {
        params = CalculationMethod.umm_al_qura.getParameters();
        await cacheMemory.write(
            CALCULATION_METHOD, calculationMethods.first.toJson());
        await cacheMemory.write(
            CALCULATION_METHOD_TITLE, calculationMethods.first.title);
      } else {
        CalculationMethodModel calculationMethodModel =
            CalculationMethodModel.fromJson(
                cacheMemory.read(CALCULATION_METHOD));
        if (calculationMethodModel.calculationMethod ==
            CalculationMethod.other) {
          params = CalculationParameters(
            fajrAngle: 18,
            ishaAngle: 17,
            method: CalculationMethod.other,
          ).withMethodAdjustments(PrayerAdjustments(
              fajr: 6, dhuhr: -2, asr: -5, maghrib: -6, isha: -12));
        } else {
          params = calculationMethodModel.calculationMethod.getParameters();
        }
      }
      update();
    } catch (e) {
      _logger.e('Error getting calculation method params: $e');
    }
  }

  Future<void> getMadhab() async {
    try {
      params.madhab = await cacheMemory.read(IS_MATHHAB_HANAFI) == true
          ? Madhab.hanafi
          : Madhab.shafi;
      update();
    } catch (e) {
      _logger.e('Error getting madhab: $e');
    }
  }

  Future<void> getHighLatitudeRule() async {
    try {
      params.highLatitudeRule = await cacheMemory.read(HIGH_LATITUDE) == null
          ? HighLatitudeRule.middle_of_the_night
          : HighLatitudeRule.values[cacheMemory.read(HIGH_LATITUDE)];
      update();
    } catch (e) {
      _logger.e('Error getting high latitude rule: $e');
    }
  }

  Duration getUtcOffset() {
    int hours = cacheMemory.read(SUMMER_TIME_HOUR) ?? 0;
    return Duration(hours: hours) + DateTime.now().timeZoneOffset;
  }

  Future<void> getAdjustments() async {
    try {
      params.adjustments = PrayerAdjustments(
        fajr: (cacheMemory.read(MANUAL_CORRECTION(FAJR)) ?? 0),
        dhuhr: (cacheMemory.read(MANUAL_CORRECTION(DHUHR)) ?? 0),
        asr: (cacheMemory.read(MANUAL_CORRECTION(ASR)) ?? 0),
        maghrib: (cacheMemory.read(MANUAL_CORRECTION(MAGHRIB)) ?? 0),
        isha: (cacheMemory.read(MANUAL_CORRECTION(ISHA)) ?? 0),
      );
      update();
    } catch (e) {
      _logger.e('Error getting adjustments: $e');
    }
  }

  Future<void> athanNotificationSetup() async {
    try {
      Map<String, AdhanModel> notificationsMap =
          SettingsController.instance.athanNotificationsMap;

      await AppNotifications.cancelOldNotificationsAndScheduleNewOnes(
          ATHAN_NOTIFICATIONS_PAYLOAD, () async {
        for (int i = 0; i < 4; i++) {
          DateTime dateTime = DateTime.now().add(Duration(days: i));
          PrayerTimes prayerTimes =
              calculatePrayerTimesByDateTimeForNotification(dateTime);

          for (String prayer in prayers) {
            if (notificationsMap[prayer]!.isNotified) {
              String? soundPath;
              if (!SettingsController.instance.isAdhanDefaultNotification()) {
                soundPath = prayer.toLowerCase() == SUNRISE.toLowerCase()
                    ? AppSounds.kBird
                    : getAdhanSoundPath(
                        AthanSoundType
                            .values[notificationsMap[prayer]!.soundIndex],
                        false,
                      );
              }
              await AppNotifications.sendScheduledNotification(
                  title: prayer.tr,
                  subtitle: prayer.toLowerCase() == SUNRISE.toLowerCase()
                      ? 'Now its time to sunrise'.tr
                      : 'Now its time to adhan'.tr,
                  time: AppFunctions.getPrayerTime(prayer, prayerTimes),
                  sound: soundPath,
                  payload: ATHAN_NOTIFICATIONS_PAYLOAD);
            }

            if (notificationsMap[prayer]!.isPreNotified) {
              await AppNotifications.sendScheduledNotification(
                  title: prayer.tr,
                  subtitle: '15 minutes for Athan'.tr,
                  time: AppFunctions.getPrayerTime(prayer, prayerTimes)
                      .subtract(const Duration(minutes: 15)),
                  sound: AppSounds.kPreAthan,
                  payload: ATHAN_NOTIFICATIONS_PAYLOAD);
            }
            if (notificationsMap[prayer]!.iqamah != null) {
              await AppNotifications.sendScheduledNotification(
                  title: prayer.tr,
                  subtitle: 'Now Its The Iqamah Time'.tr,
                  time: AppFunctions.getPrayerTime(prayer, prayerTimes).add(
                      Duration(
                          minutes: notificationsMap[prayer]!.iqamah!.toInt())),
                  sound: AppSounds.kIAssetqama,
                  payload: ATHAN_NOTIFICATIONS_PAYLOAD);
            }
          }
        }
      });
    } catch (e) {
      _logger.e('Error setting up Athan notifications: $e');
    }
  }

  Future<void> scheduleAthansWarning() async {
    if (Platform.isIOS) {
      await AppNotifications.cancelOldNotificationsAndScheduleNewOnes(
          NOTIFICATIONS_WARNING_PAYLOAD, () async {
        await AppNotifications.sendScheduledNotification(
          id: 0,
          title: 'تنبية',
          subtitle: 'يرجي فتح التطبيق للاستمرار في تلقي اشعارات الصلوات',
          time: DateTime.now().add(
            const Duration(days: 4),
          ),
          payload: NOTIFICATIONS_WARNING_PAYLOAD,
        );
      });
      debugPrint("athan scheduled");
    }
  }

  PrayerTimes calculatePrayerTimesByDateTimeForNotification(DateTime dateTime) {
    DateComponents dateComponents =
        DateComponents(dateTime.year, dateTime.month, dateTime.day);
    return PrayerTimes.utcOffset(
        coordinates, dateComponents, params, getUtcOffset());
  }

  String printPrayerTimes() {
    if (prayerTimes.value == null) return 'Calculating prayerTimes...';
    return '$currentCity ,\n${FAJR.tr} ${AppFunctions.formatTime(prayerTimes.value!.fajr)} ,\n${SUNRISE.tr} ${AppFunctions.formatTime(prayerTimes.value!.sunrise)} ,\n${DHUHR.tr} ${AppFunctions.formatTime(prayerTimes.value!.dhuhr)} ,\n${ASR.tr} ${AppFunctions.formatTime(prayerTimes.value!.asr)} ,\n${MAGHRIB.tr} ${AppFunctions.formatTime(prayerTimes.value!.maghrib)} ,\n${ISHA.tr} ${AppFunctions.formatTime(prayerTimes.value!.isha)}';
  }

  double getKaabaDistance() {
    double latitude = cacheMemory.read(LATITUDE) ?? KAABA_LATITUDE;
    double longitude = cacheMemory.read(LONGITUDE) ?? KAABA_LONGITUDE;
    return Geolocator.distanceBetween(
        latitude, longitude, kaabaLatitude, kaabaLongitude);
  }

  void resetPrayerPage() {
    prayersPageController.animateToPage(
      initialPrayerPage,
      duration: const Duration(microseconds: 250),
      curve: Curves.linear,
    );
    changePrayerDatePage(0);
  }

  Future<void> updateLocation(double latitude, double longitude) async {
    try {
      if (!isValidCoordinate(latitude, longitude)) {
        _logger.e('invalid_coordinates: $latitude, $longitude');
        Get.snackbar(
          'Error'.tr,
          'Invalid coordinates'.tr,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // Create a position object
      Position position = Position(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );

      // Update location in LocationService
      await locationService.updateLocation(position);

      // Update PrayerController's latitude and longitude with the provided values.
      this.latitude = latitude; // Class variable latitude is updated
      this.longitude = longitude; // Class variable longitude is updated

      // Re-create Coordinates using the updated latitude and longitude.
      coordinates = Coordinates(latitude, longitude); // coordinates is updated

      await cacheMemory.write(LATITUDE, latitude);
      await cacheMemory.write(LONGITUDE, longitude);

      await getCurrentCityUsingGeocode();
      await calculateQiblahDegree();
      await calculatePrayerTimes();
      update();
    } catch (e) {
      _logger.e('Update location failed: $e');
      Get.snackbar(
        'Error'.tr,
        'Update failed'.tr,
        duration: const Duration(seconds: 3),
      );
    }
    update();
  }

  void changePrayerDatePage(int page) {
    prayerDatePage.value = page;
    getDateTime();
    justCalculateTimes();
    update();
  }
}

// Custom Exceptions

class PermanentLocationPermissionDeniedException implements Exception {
  final String message;
  PermanentLocationPermissionDeniedException(this.message);
}

class LocationFetchException implements Exception {
  final String message;
  LocationFetchException(this.message);
}

class InvalidCoordinatesException implements Exception {
  final double latitude;
  final double longitude;
  InvalidCoordinatesException(this.latitude, this.longitude);

  @override
  String toString() => '${'invalid_coordinates:'.tr} $latitude, $longitude';
}
