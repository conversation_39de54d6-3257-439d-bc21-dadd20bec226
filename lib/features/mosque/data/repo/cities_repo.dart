import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

class CitiesRepo {
  static const _databaseName = 'cities.sqlite';
  static const _databaseVersion = 3; // Increase this version number

  static late Database db;

  static Future<void> init() async {
    try {
      await ensureDatabaseExists();
      await openDB();
      // Log successful initialization
      debugPrint('CitiesRepo Database initialized successfully');
    } catch (e) {
      debugPrint('CitiesRepo Error initializing database: $e');
      // Handle the error appropriately (e.g., show an error message to the user)
    }
  }

  static Future<void> ensureDatabaseExists() async {
    final prefs = await SharedPreferencesWithCache.create(
      cacheOptions: const SharedPreferencesWithCacheOptions(),
    );

    var currentVersion = prefs.getInt('citiesDatabaseVersion') ?? -1;
    debugPrint(
        'CitiesRepo name: $_databaseName. version:$_databaseVersion. currentVersion:$currentVersion');
    if (_databaseVersion == currentVersion) {
      if (kDebugMode) {
        debugPrint('aminCitiesRepo Using existing database for CitiesRepo');
      }
      return;
    }

    await prefs.setInt('citiesDatabaseVersion', _databaseVersion);

    var databasesPath = await getDatabasesPath();
    var path = join(databasesPath, _databaseName);
    var exists = await databaseExists(path);

    if (exists) {
      File(path).deleteSync();
    }

    // Should happen only the first time you launch your application
    if (kDebugMode) {
      debugPrint('CitiesRepo Creating new copy from asset');
    }

    // Make sure the parent directory exists
    try {
      await Directory(dirname(path)).create(recursive: true);
    } catch (_) {}
    // Copy from asset

    var data = await rootBundle.load(join('assets/dbs', _databaseName));
    List<int> bytes =
        data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

    debugPrint(
        'CitiesRepo name: $_databaseName. version:$_databaseVersion. currentVersion:$currentVersion');

    // Write and flush the bytes written
    await File(path).writeAsBytes(bytes, flush: true);
  }

  static Future<void> openDB() async {
    db = await openDatabase(
      join(await getDatabasesPath(), _databaseName),
      version: _databaseVersion,
      // onCreate: _onCreate,
      // onUpgrade: _onUpgrade,
    );
  }

  // static Future<void> _onCreate(Database db, int version) async {
  //   await db.execute('''
  //   CREATE TABLE IF NOT EXISTS cities (
  //     city_id INTEGER PRIMARY KEY,
  //     geo_names_id INTEGER,
  //     country_id NUMERIC,
  //     name TEXT,
  //     arabic_name TEXT,
  //     longitude NUMERIC,
  //     latitude NUMERIC,
  //     time_zone NUMERIC,
  //     iscites15000 INTEGER,
  //     is_parent INTEGER,
  //     parent_id INTEGER,
  //     level TEXT
  //   )
  // ''');

  //   await db.execute('''
  //   CREATE TABLE IF NOT EXISTS countries (
  //     country_id NUMERIC PRIMARY KEY,
  //     name TEXT,
  //     arabic_name TEXT,
  //     calculation_method NUMERIC,
  //     daylight_saving NUMERIC
  //   )
  // ''');
  // }

  // static Future<void> _onUpgrade(
  //     Database db, int oldVersion, int newVersion) async {
  //   if (oldVersion < 2) {
  //     // Add country_id column to cities table
  //     await db.execute('ALTER TABLE cities ADD COLUMN country_id NUMERIC');

  //     // Create countries table
  //     await db.execute('''
  //       CREATE TABLE countries (
  //         country_id NUMERIC PRIMARY KEY,
  //         name TEXT,
  //         arabic_name TEXT,
  //         calculation_method NUMERIC,
  //         daylight_saving NUMERIC
  //       )
  //     ''');
  //   }
  // }
}
