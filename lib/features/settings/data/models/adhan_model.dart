import '../../../../core/utils/app_notifications.dart';

class AdhanModel {
  final String title;
  bool isFullAdhan;
  bool isNotified;
  bool isPreNotified;
  double? iqamah;
  AthanSoundType athanSoundType;
  int soundIndex;
  bool isSilent;

  AdhanModel({
    required this.title,
    this.isFullAdhan = false,
    this.isPreNotified = false,
    this.isNotified = false,
    this.iqamah,
    this.athanSoundType = AthanSoundType.athan4,
    this.soundIndex = 0,
    this.isSilent = false,
  });

  // Convert AdhanModel to Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'isFullAdhan': isFullAdhan,
      'isNotified': isNotified,
      'isPreNotified': isPreNotified,
      'iqamah': iqamah,
      'athanSoundType': athanSoundType.index,
      'soundIndex': soundIndex,
      'isSilent': isSilent
    };
  }

  // Convert Map to AdhanModel
  static AdhanModel fromMap(title, Map<String, dynamic>? map) {
    if (map == null) {
      return AdhanModel(title: title);
    }
    return AdhanModel(
      title: map['title'],
      isFullAdhan: map['isFullAdhan'] ?? false,
      isNotified: map['isNotified'] ?? false,
      isPreNotified: map['isPreNotified'] ?? false,
      iqamah: map['iqamah'],
      athanSoundType: AthanSoundType.values[map['athanSoundType'] ?? 1],
      soundIndex: map['soundIndex'] as int? ?? 0,
      isSilent: map['isSilent'] ?? false,
    );
  }

  factory AdhanModel.fromJson(Map<String, dynamic> json) => AdhanModel(
        title: json['title'] as String,
        isNotified: json['isNotified'] as bool,
        isPreNotified: json['isPreNotified'] as bool,
        isFullAdhan: json['isFullAdhan'] as bool,
        iqamah: json['iqamah'] as double?,
        soundIndex: json['soundIndex'] as int? ?? 0,
        isSilent: json['isSilent'] as bool? ?? false,
      );
  Map<String, dynamic> toJson() => {
        'title': title,
        'isNotified': isNotified,
        'isPreNotified': isPreNotified,
        'isFullAdhan': isFullAdhan,
        'iqamah': iqamah,
        'soundIndex': soundIndex,
        'isSilent': isSilent
      };
}
