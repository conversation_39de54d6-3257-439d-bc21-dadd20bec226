import 'package:auto_size_text/auto_size_text.dart';
import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/core/widgets/wraper_widget.dart';
import 'package:salawati/features/location/data/services/location_service.dart';
import 'package:salawati/features/qiblah/presentation/screens/qiblah_screen.dart';
import 'package:salawati/features/qiblah/presentation/widgets/compass_widget2.dart';
import 'package:salawati/features/qiblah/presentation/widgets/qiblah_maps.dart';
import 'package:salawati/features/qiblah/presentation/widgets/rive_widget.dart';

class QiblahScreen extends StatefulWidget {
  const QiblahScreen({super.key});
  @override
  State<QiblahScreen> createState() => _QiblahScreenState();
}

class _QiblahScreenState extends State<QiblahScreen> {
  int pageIndex = 0;

  double getAspectRatioForScreenSize(BuildContext context) {
    final Size screenSize = MediaQuery.of(context).size;
    final double screenWidth = screenSize.width;
    final double screenHeight = screenSize.height;

    // Check if this is a large screen like iPhone Pro Max 16
    // iPhone Pro Max 16 has width around 430 logical pixels and height around 932 logical pixels
    if (screenWidth >= 390 && screenHeight >= 844) {
      return 1; // Use 0.99 for larger screens
    } else {
      return 0.95; // Use 0.75 for smaller screens
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    Get.put(CompassController());

    // Request location permission and get location
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLocation();
    });
  }

  Future<void> _initializeLocation() async {
    try {
      // Get LocationService instance
      final locationService = Get.find<LocationService>();

      // Enable auto location and request permission
      if (!locationService.hasLocationAccess) {
        final result = await locationService.handlePermissionFlow();
        if (result != LocationPermissionResult.granted) {
          throw Exception('Location permission not granted');
        }
      }

      // Get current position
      await locationService.getCurrentPosition();
    } catch (e) {
      debugPrint('QiblahScreen: Error initializing location: $e');
      // Show a snackbar to inform the user
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Could not get location. Qiblah direction may not be accurate.'),
            duration: Duration(seconds: 3),
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.sizeOf(context).height;

    return Scaffold(
      body: SmoothEdgesContainer(
        borderRadius: BorderRadius.vertical(top: Radius.circular(60.r)),
        color: Theme.of(context).scaffoldBackgroundColor,
        child: WrapperWidget(
          condition: pageIndex == 0,
          widget: (c) => SingleChildScrollView(child: c),
          fallBack: (c) => c,
          child: Column(
            children: [
              SizedBox(
                height: height * 0.79,
                child: ExpandablePageView(
                  controller: PageController(initialPage: pageIndex),
                  onPageChanged: (i) => setState(() => pageIndex = i),
                  children: [
                    // ─── Compass Page ───────────────────────
                    Stack(
                      alignment: Alignment.topCenter,
                      clipBehavior: Clip.none,
                      children: [
                        Column(
                          children: [
                            Padding(
                              padding:
                                  EdgeInsets.only(top: height * 0.27, left: 20),
                              child: AspectRatio(
                                aspectRatio:
                                    getAspectRatioForScreenSize(context),
                                child: QiblahRiveWidget(),
                              ),
                            ),
                            SizedBox(height: 20),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: List.generate(2, (i) {
                                return Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 4.w),
                                  child: CircleAvatar(
                                    backgroundColor: pageIndex == i
                                        ? AppColor.kWhiteColor
                                        : AppColor.kRectangleColor,
                                    radius: 4.r,
                                  ),
                                );
                              }),
                            ),
                          ],
                        ),
                        const QiblahHeader(isMap: false),
                      ],
                    ),
                    // ─── Map Page ──────────────────────────
                    Stack(
                      alignment: Alignment.topCenter,
                      clipBehavior: Clip.none,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: height * 0.1),
                          child: const AspectRatio(
                            aspectRatio: 0.75,
                            child: QiblahMaps(),
                          ),
                        ),
                        const QiblahHeader(isMap: true),
                        Positioned(
                          bottom: -20.h,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(2, (i) {
                              return Padding(
                                padding: EdgeInsets.symmetric(horizontal: 4.w),
                                child: CircleAvatar(
                                  backgroundColor: pageIndex == i
                                      ? AppColor.kWhiteColor
                                      : AppColor.kRectangleColor,
                                  radius: 4.r,
                                ),
                              );
                            }),
                          ),
                        ),
                     
                     
                      ],
                    ),
                
                
                  ],
                ),
              ),
              const CompassCalibrationPopup(),
            ],
          ),
        ),
      ),
    );
  }
}

class QiblahOptions extends StatelessWidget {
  final String option;
  final String optionValue;
  final bool isWarning;
  final Widget? extra;
  const QiblahOptions({
    super.key,
    required this.option,
    required this.optionValue,
    this.isWarning = false,
    this.extra,
  });

  @override
  Widget build(BuildContext context) {
    var warningColor = AppColor.kRedColor.withAlpha(51); // ~0.2 opacity
    Widget orangeVertical = Row(
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Container(
              height: 20,
              width: 2,
              color: isWarning ? AppColor.kRedColor : AppColor.kOrangeColor,
            )),
        const SizedBox(width: 10),
      ],
    );
    return Container(
      width: 155,
      height: 79,
      decoration: BoxDecoration(
          color: isWarning ? warningColor : AppColor.kRectangleColor,
          borderRadius: BorderRadius.circular(15.r),
          border: Border.all(
            width: 0.5,
            color: AppColor.kGreyColor.withAlpha(51), // ~0.2 opacity
          )),
      child: ClipPath(
        clipper: ShapeBorderClipper(
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(21.r),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  orangeVertical,
                  CustomText(
                    option,
                    style: TextStyle(
                        fontSize: 12,
                        color: isWarning
                            ? const Color(0xffF2B8B5)
                            : AppColor.kGreyColor),
                  ),
                  const Spacer(),
                  if (extra != null) extra!,
                ],
              ),
              8.verticalSpace,
              Row(
                children: [
                  Expanded(
                    child: AutoSizeText(
                      optionValue.tr,
                      maxLines: 2,
                      maxFontSize: 18,
                      style: TextStyle(
                        color: isWarning
                            ? AppColor.kRedColor
                            : AppColor.kWhiteColor,
                        fontWeight: FontWeight.normal,
                        fontSize: 18.sp,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// class MyNewCampass extends StatelessWidget {
//   const MyNewCampass({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return DefaultTextStyle(
//       style: Theme.of(context).textTheme.headlineSmall!,
//       child: Center(
//         child: StreamBuilder<CompassXEvent>(
//           stream: CompassX.events,
//           builder: (context, snapshot) {
//             if (snapshot.hasError) return Text(snapshot.error.toString());
//             if (!snapshot.hasData) return const CircularProgressIndicator();
//             final compass = snapshot.data!;
//             return Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Text(Get.find<PrayerController>().qiblahDegree.toString()),
//                 Text('Heading: ${compass.heading}'),
//                 Text('Accuracy: ${compass.accuracy}'),
//                 Text('Should calibrate: ${compass.shouldCalibrate}'),
//                 Transform.rotate(
//                   angle: compass.heading * math.pi / 180,
//                   child: SvgPicture.asset(
//                     AppSvgs.kQiblah,
//                     height: 70.w,
//                     width: 70.w,
//                   ),
//                 ),
//                 FilledButton(
//                   onPressed: () async {
//                     if (!Platform.isAndroid) return;
//                     await Permission.location.request();
//                   },
//                   child: const Text('Request permission'),
//                 ),
//               ],
//             );
//           },
//         ),
//       ),
//     );
//   }
// }
