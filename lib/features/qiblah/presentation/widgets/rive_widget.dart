import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'package:get/get.dart';
import 'package:rive_native/rive_native.dart' as rive;
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/main.dart';

String getDirectionLabel({
  required double? degree,
  String languageCode = 'en',
}) {
  const dirMap = {
    'en': ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'],
    'ar': ['ش', 'ش/ش', 'ش/ر', 'ج/ر', 'ج', 'ج/غ', 'غ', 'ش/غ'],
    'fr': ['N', 'NE', 'E', 'SE', 'S', 'SO', 'O', 'NO'],
    'ur': ['ش', 'ش/ش', 'ش/ر', 'ج/ر', 'ج', 'ج/غ', 'غ', 'ش/غ'],
  };

  final directions = dirMap[languageCode] ?? dirMap['en']!;

  final normalized = degree ?? 0 % 360;
  final index = ((normalized / 45).round()) % 8;

  return directions[index];
}

class CompassController extends GetxController {
  StreamSubscription<double>? _compassSub;
  final RxDouble heading = 0.0.obs;

  double? _smoothed;
  double? _lastV;
  static const _alpha = 0.2;
  static const _velFactor = 0.3;

  rive.ViewModelInstance? _vm;
  rive.ViewModelInstanceNumber? _degVM;
  rive.ViewModelInstanceNumber? _startVM;
  rive.ViewModelInstanceNumber? _kappahRotation;

  rive.ViewModelInstanceTrigger? _clickVM;
  DateTime? _lastClickTime;
  double? _lastRaw;
  int _revolutions = 0;
  @override
  void onClose() {
    stop();
    _vm?.dispose();
    super.onClose();
  }

  void start() {
    if (_compassSub != null) return;

    try {
      // Check if compass events are available
      if (FlutterCompass.events == null) {
        debugPrint('CompassController: Compass events not available');
        return;
      }

      _compassSub = FlutterCompass.events!
          .where((e) => e.heading != null)
          .map((e) => (e.heading! + 360) % 360)
          .listen(_handleRawHeading, onError: (e) {
        debugPrint('CompassController: Error in compass stream: $e');
      });

      final h = heading.value;
      _degVM?.value = h;
      _startVM?.value = _calcStart(h);

      // Make sure PrayerController is registered
      if (Get.isRegistered<PrayerController>()) {
        // Use the actual Qiblah degree without negating it
        _kappahRotation?.value =
            (Get.find<PrayerController>().qiblahDegree.value ?? 0);
      }
    } catch (e) {
      debugPrint('CompassController: Error starting compass: $e');
    }
  }

  void stop() {
    _compassSub?.cancel();
    _compassSub = null;
  }

  void bindRiveVM(rive.ViewModelInstance vm) {
    try {
      _vm = vm;

      // Safely get view model properties
      try {
        _degVM = vm.number('compassDegree');
      } catch (e) {
        Get.log('CompassController: Error getting compassDegree: $e');
      }

      try {
        _startVM = vm.number('start');
      } catch (e) {
        debugPrint('CompassController: Error getting start: $e');
      }

      try {
        _clickVM = vm.trigger('click');
      } catch (e) {
        debugPrint('CompassController: Error getting click: $e');
      }

      try {
        _kappahRotation = vm.number('kappahRotation');
      } catch (e) {
        debugPrint('CompassController: Error getting kappahRotation: $e');
      }

      final h = heading.value;

      // Safely set view model values
      if (_degVM != null) {
        _degVM!.value = h;
      }

      if (_startVM != null) {
        _startVM!.value = _calcStart(h);
      }

      if (_kappahRotation != null && Get.isRegistered<PrayerController>()) {
        // Use the actual Qiblah degree without negating it
        _kappahRotation!.value =
            (Get.find<PrayerController>().qiblahDegree.value ?? 0);
      }
    } catch (e) {
      debugPrint('CompassController: Error binding Rive view model: $e');
    }
  }

  void _handleRawHeading(double raw) {
    try {
      if (_lastRaw != null) {
        final delta = raw - _lastRaw!;
        if (delta > 180) {
          _revolutions--;
        } else if (delta < -180) {
          _revolutions++;
        }
      }
      _lastRaw = raw;

      final unwrapped = raw + _revolutions * 360;

      if (_smoothed == null) {
        _smoothed = unwrapped;
      } else {
        final diff = unwrapped - _smoothed!;
        final vel = diff * _velFactor;
        _smoothed = _smoothed! + _alpha * diff + (_lastV ?? vel) * (1 - _alpha);
        _lastV = vel;
      }

      // Safely get qiblah degree
      double qiblahDegree = 0;
      if (Get.isRegistered<PrayerController>()) {
        qiblahDegree = Get.find<PrayerController>().qiblahDegree.value ?? 0;
      }

      final displayAngle = _smoothed!;
      heading.value = double.parse(displayAngle.toStringAsFixed(2));

      // Log compass and qiblah values for debugging
      debugPrint(
          'CompassController: Current heading: ${heading.value}, Qiblah: $qiblahDegree');

      // Update view model values if they exist
      if (_degVM != null) {
        _degVM!.value = heading.value - qiblahDegree;
        debugPrint(
            'CompassController: Setting degVM to: ${heading.value - qiblahDegree}');
      }

      if (_startVM != null) {
        _startVM!.value = _calcStart(heading.value);
      }
    } catch (e) {
      debugPrint('CompassController: Error handling heading: $e');
    }
  }

  double _calcStart(double h) {
    try {
      // Safely get qiblah degree
      double threshold = 0;
      if (Get.isRegistered<PrayerController>()) {
        threshold = Get.find<PrayerController>().qiblahDegree.value ?? 0;
      }

      const isDone = 40.0;

      double rawDiff = (h - threshold + 360) % 360;
      if (rawDiff > 180) rawDiff -= 360;

      double absClamped = rawDiff.abs().clamp(0.0, isDone);
      double finalValue = (isDone - absClamped);

      debugPrint(
          'CompassController: _calcStart - heading: $h, qiblah: $threshold, rawDiff: $rawDiff, finalValue: $finalValue');

      return finalValue;
    } catch (e) {
      debugPrint('CompassController: Error calculating start: $e');
      return 0.0; // Default value in case of error
    }
  }

  void fireClickThrottled() {
    final now = DateTime.now();

    if (_lastClickTime == null ||
        now.difference(_lastClickTime!).inSeconds >= 2) {
      _lastClickTime = now;
      _clickVM?.value = true;
    }
  }
}

class QiblahRiveWidget extends StatefulWidget {
  const QiblahRiveWidget({super.key});
  @override
  QiblahRiveWidgetState createState() => QiblahRiveWidgetState();
}

class QiblahRiveWidgetState extends State<QiblahRiveWidget> with RouteAware {
  late final CompassController _ctrl;
  bool _isActive = false;

  @override
  void initState() {
    super.initState();
    _ctrl = Get.find<CompassController>();

    // Start the compass when the widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startCompass();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Subscribe to route changes
    routeObserver.subscribe(
      this,
      ModalRoute.of(context)!,
    );
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    // _stopCompass();
    super.dispose();
  }

  void _startCompass() {
    if (!_isActive) {
      _isActive = true;
      _ctrl.start();

      // Force update of the Qiblah direction
      final prayerController = Get.find<PrayerController>();
      // Use the actual Qiblah degree without negating it
      _ctrl._kappahRotation?.value = (prayerController.qiblahDegree.value ?? 0);

      debugPrint('QiblahRiveWidget: Compass started');
    }
  }

  void _stopCompass() {
    if (_isActive) {
      _isActive = false;
      _ctrl.stop();
      debugPrint('QiblahRiveWidget: Compass stopped');
    }
  }

  @override
  void didPush() {
    debugPrint('QiblahRiveWidget: didPush');
    _startCompass();
  }

  @override
  void didPopNext() {
    debugPrint('QiblahRiveWidget: didPopNext');
    // This is called when returning to this screen
    _startCompass();

    // Force a rebuild to ensure the Rive animation is properly updated
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didPushNext() {
    debugPrint('QiblahRiveWidget: didPushNext');
    _stopCompass();
  }

  @override
  void didPop() {
    debugPrint('QiblahRiveWidget: didPop');
    _stopCompass();
  }

  double getAspectRatioForScreenSize(BuildContext context) {
    final Size screenSize = MediaQuery.of(context).size;
    final double screenWidth = screenSize.width;
    final double screenHeight = screenSize.height;

    // Define min and max screen dimensions to consider
    const double minWidth = 320.0; // Smaller phone width (e.g., iPhone SE)
    const double maxWidth = 430.0; // Larger phone width (e.g., iPhone Pro Max)
    const double minHeight = 568.0; // Smaller phone height
    const double maxHeight = 932.0; // Larger phone height

    // Calculate a normalized value between 0 and 1 based on screen size
    // We'll use both width and height to determine the device size factor
    double widthFactor = (screenWidth - minWidth) / (maxWidth - minWidth);
    double heightFactor = (screenHeight - minHeight) / (maxHeight - minHeight);

    // Clamp the factors between 0 and 1
    widthFactor = widthFactor.clamp(0.0, 1.0);
    heightFactor = heightFactor.clamp(0.0, 1.0);

    // Use the average of width and height factors
    double sizeFactor = (widthFactor + heightFactor) / 2;

    // Interpolate between 1.7 (for small devices) and 1.5 (for large devices)
    return 1.7 - (sizeFactor * 0.2);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _ctrl.fireClickThrottled();
      },
      child: TickerMode(
        enabled: true,
        child: RivePlayer(
          // key: ValueKey(
          //     'qiblah_rive_${DateTime.now().millisecondsSinceEpoch}'),
          asset: 'assets/json/untitled.riv',
          artboardName: 'backup 3',
          fit: rive.Fit.contain,
          layoutScaleFactor: 1 / getAspectRatioForScreenSize(context),
          stateMachineName: 'State Machine 1',
          autoBind: true,
          withViewModelInstance: _ctrl.bindRiveVM,
        ),
      ),
    );
  }
}

class RivePlayer extends StatefulWidget {
  const RivePlayer({
    super.key,
    required this.asset,
    this.stateMachineName,
    this.artboardName,
    this.hitTestBehavior = rive.RiveHitTestBehavior.opaque,
    this.cursor = MouseCursor.defer,
    this.fit = rive.Fit.contain,
    this.alignment = Alignment.center,
    this.layoutScaleFactor = 1.0,
    this.withArtboard,
    this.withStateMachine,
    this.withViewModelInstance,
    this.assetLoader,
    this.autoBind = true,
  });
  final String asset;
  final String? stateMachineName;
  final String? artboardName;
  final rive.RiveHitTestBehavior hitTestBehavior;
  final MouseCursor cursor;
  final rive.Fit fit;
  final Alignment alignment;
  final double layoutScaleFactor;
  final rive.AssetLoaderCallback? assetLoader;
  final bool autoBind;

  final void Function(rive.StateMachine stateMachine)? withStateMachine;
  final void Function(rive.Artboard artboard)? withArtboard;
  final void Function(rive.ViewModelInstance viewModelInstance)?
      withViewModelInstance;

  @override
  State<RivePlayer> createState() => _RivePlayerState();
}

class _RivePlayerState extends State<RivePlayer> {
  rive.File? riveFile;

  late rive.Artboard artboard;
  late rive.StateMachinePainter stateMachinePainter;

  @override
  void initState() {
    super.initState();
    init();
  }

  Future<void> init() async {
    riveFile = await _loadFile();
    if (riveFile == null) return;

    if (widget.artboardName != null) {
      artboard = riveFile!.artboard(widget.artboardName!)!;
    } else {
      artboard = riveFile!.artboardAt(0)!;
    }
    widget.withArtboard?.call(artboard);

    stateMachinePainter = rive.RivePainter.stateMachine(
      stateMachineName: widget.stateMachineName,
      withStateMachine: (stateMachine) {
        widget.withStateMachine?.call(stateMachine);
        if (!widget.autoBind) return;
        final vm = riveFile!.defaultArtboardViewModel(artboard);
        if (vm == null) {
          return;
        }
        final vmi = vm.createDefaultInstance();
        if (vmi == null) {
          return;
        }
        stateMachine.bindViewModelInstance(vmi);
        widget.withViewModelInstance?.call(vmi);
      },
    )
      ..hitTestBehavior = widget.hitTestBehavior
      ..cursor = widget.cursor
      ..fit = widget.fit
      ..alignment = widget.alignment
      ..layoutScaleFactor = widget.layoutScaleFactor;

    setState(() {});
  }

  Future<rive.File?> _loadFile() async {
    final bytes = await rootBundle.load(widget.asset);
    return rive.File.decode(
      bytes.buffer.asUint8List(),
      riveFactory: rive.Factory.rive,
      assetLoader: widget.assetLoader,
    );
  }

  @override
  void didUpdateWidget(RivePlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.hitTestBehavior != oldWidget.hitTestBehavior) {
      stateMachinePainter.hitTestBehavior = widget.hitTestBehavior;
    }
    if (widget.cursor != oldWidget.cursor) {
      stateMachinePainter.cursor = widget.cursor;
    }
    if (widget.fit != oldWidget.fit) {
      stateMachinePainter.fit = widget.fit;
    }
    if (widget.alignment != oldWidget.alignment) {
      stateMachinePainter.alignment = widget.alignment;
    }
    if (widget.layoutScaleFactor != oldWidget.layoutScaleFactor) {
      stateMachinePainter.layoutScaleFactor = widget.layoutScaleFactor;
    }
  }

  @override
  Widget build(BuildContext context) {
    return riveFile != null
        ? rive.RiveArtboardWidget(
            artboard: artboard,
            painter: stateMachinePainter,
          )
        : const SizedBox();
  }

  @override
  void dispose() {
    artboard.dispose();
    riveFile?.dispose();
    super.dispose();
  }
}
