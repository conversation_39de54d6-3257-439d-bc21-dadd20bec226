import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/permissions/presentation/controller/permission_flow_controller.dart';
import 'package:salawati/features/permissions/presentation/widgets/permission_card.dart';

class LocationPermissionScreen extends GetView<PermissionFlowController> {
  const LocationPermissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            
            // Content
            Column(
              children: [
                // Header
                _buildHeader(),
                
                // Progress indicator
                _buildProgressIndicator(),
                
                32.verticalSpace,
                
                // Permission card
                Expanded(
                  child: Center(
                    child: SingleChildScrollView(
                      child: Obx(() => PermissionCard(
                        icon: controller.getStepIcon(PermissionStep.location),
                        title: controller.getStepTitle(PermissionStep.location),
                        description: controller.getStepDescription(PermissionStep.location),
                        buttonText: controller.getStepButtonText(PermissionStep.location),
                        onAllow: controller.requestCurrentPermission,
                        onSkip: controller.canSkip ? controller.skipCurrentPermission : null,
                        isLoading: controller.isLoading,
                        errorMessage: controller.errorMessage.isEmpty ? null : controller.errorMessage,
                        onClearError: controller.clearError,
                        canSkip: controller.canSkip,
                      )),
                    ),
                  ),
                ),
                
                // Skip all button
                _buildSkipAllButton(),
                
                24.verticalSpace,
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          // Back button (disabled for first screen)
          SizedBox(
            width: 40.w,
            height: 40.w,
            child: Container(), // Empty container to maintain spacing
          ),
          
          Expanded(
            child: CustomText(
              'Setup Permissions',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.kPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // Close button
          GestureDetector(
            onTap: () => _showSkipDialog(),
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColor.kRectangleColor.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                color: AppColor.kGreyColor,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // Progress bar
          Obx(() {
            final progress = controller.getProgress();
            return Container(
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColor.kGreyColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColor.kPrimaryColor,
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              ),
            );
          }),
          
          8.verticalSpace,
          
          // Step indicator
          CustomText(
            'Step 1 of 3',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColor.kGreyColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkipAllButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: TextButton(
        onPressed: () => _showSkipDialog(),
        child: CustomText(
          'Skip all permissions',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

  void _showSkipDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: CustomText(
          'Skip Permissions?',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: CustomText(
          'You can always enable permissions later in Settings. However, some features may not work properly without the required permissions.',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.kGreyColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: CustomText(
              'Cancel',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kGreyColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.skipAllPermissions();
            },
            child: CustomText(
              'Skip All',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.kPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
