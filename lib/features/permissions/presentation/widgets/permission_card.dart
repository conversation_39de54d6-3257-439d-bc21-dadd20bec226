import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';

class PermissionCard extends StatelessWidget {
  final String icon;
  final String title;
  final String description;
  final String buttonText;
  final VoidCallback onAllow;
  final VoidCallback? onSkip;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onClearError;
  final bool canSkip;

  const PermissionCard({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    required this.buttonText,
    required this.onAllow,
    this.onSkip,
    this.isLoading = false,
    this.errorMessage,
    this.onClearError,
    this.canSkip = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColor.kRectangleColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: AppColor.kOrangeColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                icon,
                style: TextStyle(fontSize: 40.sp),
              ),
            ),
          ),

          24.verticalSpace,

          // Title
          CustomText(
            title,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.kOrangeColor,
            ),
            textAlign: TextAlign.center,
          ),

          16.verticalSpace,

          // Description
          CustomText(
            description,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColor.kGreyColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          // Error message
          if (errorMessage != null && errorMessage!.isNotEmpty) ...[
            16.verticalSpace,
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.red.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 20.sp,
                  ),
                  8.horizontalSpace,
                  Expanded(
                    child: CustomText(
                      errorMessage!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.red,
                      ),
                    ),
                  ),
                  if (onClearError != null)
                    GestureDetector(
                      onTap: onClearError,
                      child: Icon(
                        Icons.close,
                        color: Colors.red,
                        size: 18.sp,
                      ),
                    ),
                ],
              ),
            ),
          ],

          32.verticalSpace,

          // Buttons
          Column(
            children: [
              // Allow button
              SizedBox(
                width: double.infinity,
                height: 50.h,
                child: ElevatedButton(
                  onPressed: isLoading ? null : onAllow,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColor.kOrangeColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 0,
                  ),
                  child: isLoading
                      ? SizedBox(
                          width: 20.w,
                          height: 20.w,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : CustomText(
                          buttonText,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),

              // Skip button
              if (canSkip && onSkip != null) ...[
                12.verticalSpace,
                SizedBox(
                  width: double.infinity,
                  height: 50.h,
                  child: TextButton(
                    onPressed: isLoading ? null : onSkip,
                    style: TextButton.styleFrom(
                      foregroundColor: AppColor.kGreyColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: CustomText(
                      'Skip for now',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
