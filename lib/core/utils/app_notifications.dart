import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:alarm/alarm.dart';

import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_battery_optimiztion.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class AppNotifications {
  static final AppNotifications _instance = AppNotifications._internal();

  factory AppNotifications() => _instance;

  AppNotifications._internal();

  static Future<void> configureLocalTimeZone() async {
    tz.initializeTimeZones();
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  static Future<void> testAlarm2() async {
    await configureLocalTimeZone();

    var sound = getAdhanSoundPath(AthanSoundType.values[1], false);

    var androidSound = sound?.substring(0, sound.indexOf("."));

    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      'scheduled title',
      'scheduled body',
      tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
      NotificationDetails(
        android: AndroidNotificationDetails(
          "${androidSound}_salawati_channel",
          'تطبيق صلواتي',
          channelDescription: 'تطبيق صلواتي',
          playSound: true,
          autoCancel: false,
          showProgress: true,
          importance: Importance.max,
          priority: Priority.high,
          sound: RawResourceAndroidNotificationSound(androidSound),
          ongoing: true,
          visibility: NotificationVisibility.public,
          category: AndroidNotificationCategory.alarm,
          actions: [
            AndroidNotificationAction(
              'salati_id_1',
              'cancel'.tr,
              icon: const DrawableResourceAndroidBitmap('ic_launcher'),
              showsUserInterface: true,
              cancelNotification: true,
            ),
          ],
        ),
        iOS: DarwinNotificationDetails(sound: sound),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      // uiLocalNotificationDateInterpretation:
      //     UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static Future<void> initLocaleNotification() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings initializationSettingsIos =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIos,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationSettings);

    // Add a delay before requesting permissions
    await Future.delayed(const Duration(seconds: 2));

    // Check and request notification permissions

    await checkAndRequestNotificationPermissions();

    if (Platform.isAndroid) {
      final androidPlugin =
          flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      try {
        await androidPlugin?.requestExactAlarmsPermission();
      } catch (e) {
        debugPrint("Exact alarm error: $e");
      }
    }

    await AppBatteryOptimization.initAutoStart();

    debugPrint('Notification permissions granted');
  }

  static Future<bool> checkAndRequestNotificationPermissions() async {
    try {
      // Check current permission status
      var status = await Permission.notification.status;
      if (status.isGranted) {
        debugPrint('Notification permissions already granted');
        return true;
      }
      // Request permission
      status = await Permission.notification.request();
      if (status.isGranted) return true;

      // Show custom dialog if denied
      await _showNotificationPermissionDialog();
      return false;
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      return false;
    }
  }

  static Future<void> _showNotificationPermissionDialog() async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('enable_notifications'.tr),
        content: CustomText(
          'notification_settings_request'.tr,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: CustomText('Cancel'.tr),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              await AppSettings.openAppSettings(
                  type: AppSettingsType.notification);
            },
            child: CustomText('openSettings'.tr),
          ),
        ],
      ),
      barrierDismissible: false, // Force user interaction
    );
  }

  static Future<void> sendScheduledNotification({
    int? id,
    required String title,
    required String subtitle,
    required DateTime time,
    String? sound,
    required String payload,
  }) async {
    tz.initializeTimeZones();
    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    time = DateTime.parse(
      time.toIso8601String().replaceFirst("z", "").replaceFirst("Z", ''),
    );

    if (tz.TZDateTime.from(time, tz.local)
        .isBefore(tz.TZDateTime.now(tz.local))) {
      return;
    }

    var androidSound = sound?.substring(0, sound.indexOf(".")) ?? "";

    await flutterLocalNotificationsPlugin.zonedSchedule(
      id ?? DateTime.now().millisecondsSinceEpoch % 1000 + 1,
      title,
      subtitle,
      tz.TZDateTime.from(time, tz.local),
      NotificationDetails(
        android: AndroidNotificationDetails(
            "${androidSound}_salawati_channel", 'تطبيق صلواتي',
            channelDescription: 'تطبيق صلواتي',
            playSound: true,
            enableVibration: true,
            sound: sound == null
                ? null
                : RawResourceAndroidNotificationSound(androidSound),
            importance: Importance.max,
            priority: Priority.high,
            ongoing: true,
            autoCancel: false,
            visibility: NotificationVisibility.public,
            category: AndroidNotificationCategory.alarm,
            fullScreenIntent: true,
            actions: [
              AndroidNotificationAction(
                'salati_id_1',
                'cancel'.tr,
                icon: const DrawableResourceAndroidBitmap('ic_launcher'),
                showsUserInterface: true,
                cancelNotification: true,
              ),
            ]),
        iOS: DarwinNotificationDetails(
          sound: sound,
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          interruptionLevel: InterruptionLevel.timeSensitive,
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      // uiLocalNotificationDateInterpretation:
      //     UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
    );
  }

  // static Future<void> showNotifications(
  //   String? sound,
  //   List<String> messages,
  //   int id,
  //   String title,
  // ) async {
  //   const int maxPendingNotifications = 100; // Adjust as needed
  //   const int repeatIntervalInMinutes = 1; // Adjust as needed

  //   for (int i = 0; i < maxPendingNotifications; i++) {
  //     final messageIndex = i % messages.length;
  //     final message = messages[messageIndex];
  //     var androidSound = sound?.substring(0, sound.indexOf(".")) ?? "";

  //     await flutterLocalNotificationsPlugin.zonedSchedule(
  //       id + i,
  //       title,
  //       message,
  //       tz.TZDateTime.now(tz.local)
  //           .add(Duration(minutes: i * repeatIntervalInMinutes)),
  //       NotificationDetails(
  //         android: AndroidNotificationDetails(
  //           'main_channel',
  //           'Main Channel',
  //           channelDescription: 'ashwin',
  //           importance: Importance.max,
  //           priority: Priority.max,
  //           playSound: true,
  //           sound: sound == null
  //               ? null
  //               : RawResourceAndroidNotificationSound(androidSound),
  //           styleInformation: BigTextStyleInformation(message),
  //         ),
  //         iOS: const DarwinNotificationDetails(
  //           sound: 'default.wav',
  //           presentAlert: true,
  //           presentBadge: true,
  //           presentSound: true,
  //         ),
  //       ),
  //       // uiLocalNotificationDateInterpretation:
  //       //     UILocalNotificationDateInterpretation.absoluteTime,
  //       androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  //       payload: jsonEncode({'message_index': messageIndex}),
  //     );
  //   }
  // }

  static Future<void> onSelectNotification(String? payload) async {
    if (payload != null) {
      final Map<String, dynamic> payloadMap = jsonDecode(payload);
      final messageIndex = payloadMap['message_index'];
      // Handle the selected notification
      debugPrint('Selected notification with message index: $messageIndex');
    }
  }

  static Future<void> cancelAllAthanNotifications() async {
    debugPrint('amin cancelAllAthanNotifications');
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  static Future<void> cancelOldNotificationsAndScheduleNewOnes(
    String payload,
    Future<void> Function() scheduleNewNotifications,
  ) async {
    try {
      // Step 1: Cancel old notifications with the given payload
      await cancelNotificationsByPayload(payload);
      // debugPrint('Cancelled notification for: $payload ');
      // Step 2: Schedule new notifications
      await scheduleNewNotifications();

      // Step 3: Notify the user (optional)
      // Get.snackbar('تم التحديث', 'تم إعادة جدولة الإشعارات بنجاح');
    } catch (e) {
      // Handle errors gracefully
      debugPrint('Error in cancelOldNotificationsAndScheduleNewOnes: $e');
      // Get.snackbar('Error'.tr, 'حدث خطأ أثناء إعادة جدولة الإشعارات');
    }
  }

  static Future<void> cancelNotificationsByPayload(String payload) async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    // Get all pending notifications
    final List<PendingNotificationRequest> pendingNotifications =
        await flutterLocalNotificationsPlugin.pendingNotificationRequests();

    // Filter and cancel notifications with the specified payload
    for (var notification in pendingNotifications) {
      if (notification.payload?.contains(payload) == true) {
        await flutterLocalNotificationsPlugin.cancel(notification.id);
        // debugPrint('Cancelled notification with ID: ${notification.id}');
      }
    }
  }

  static Future<bool> hasScheduledNotifications(String payload) async {
    final pending =
        await flutterLocalNotificationsPlugin.pendingNotificationRequests();
    return pending.any((n) => n.payload?.contains(payload) == true);
  }

  static void firebaseNotificationSetup() {
    firebase.FirebaseMessaging.onBackgroundMessage(
      firebaseMessagingBackgroundHandler,
    );
    firebase.FirebaseMessaging.onMessage
        .listen((firebase.RemoteMessage message) {
      AppFunctions.customSnackbar(
          message.notification!.body!, message.notification!.title!);
    });
    firebase.FirebaseMessaging.onMessageOpenedApp
        .listen((firebase.RemoteMessage message) {
      AppFunctions.customSnackbar(
          message.notification!.body!, message.notification!.title!);
    });
  }
}

enum AthanSoundType {
  athan1,
  athan2,
  athan3,
  athan4,
  silent,
}

String getAdhanName(AthanSoundType type) {
  switch (type) {
    case AthanSoundType.silent:
      return 'Use Default (Silent)'.tr;
    case AthanSoundType.athan1:
      return 'Athan 1'.tr;
    case AthanSoundType.athan2:
      return 'Athan 2'.tr;
    case AthanSoundType.athan3:
      return 'Athan 3'.tr;
    case AthanSoundType.athan4:
      return 'Athan 4'.tr;
  }
}

String? getAdhanSoundPath(AthanSoundType type, bool isFullAdhan,
    [bool fromAsset = false]) {
  if (type == AthanSoundType.silent) return null;
  if (fromAsset) {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAssetAthan4Short;
      case AthanSoundType.athan1:
        return AppSounds.kAssetAthan1Short;
      case AthanSoundType.athan2:
        return AppSounds.kAssetAthan2Short;
      case AthanSoundType.athan3:
        return AppSounds.kAssetAthan3Short;
      case AthanSoundType.silent:
        return null;
    }
  } else if (isFullAdhan) {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAthan4;
      case AthanSoundType.athan1:
        return AppSounds.kAthan1;
      case AthanSoundType.athan2:
        return AppSounds.kAthan2;
      case AthanSoundType.athan3:
        return AppSounds.kAthan3;
      case AthanSoundType.silent:
        return null;
    }
  } else {
    switch (type) {
      case AthanSoundType.athan4:
        return AppSounds.kAthan4Short;
      case AthanSoundType.athan1:
        return AppSounds.kAthan1Short;
      case AthanSoundType.athan2:
        return AppSounds.kAthan2Short;
      case AthanSoundType.athan3:
        return AppSounds.kAthan3Short;
      case AthanSoundType.silent:
        return null;
    }
  }
}

Future<void> firebaseMessagingBackgroundHandler(
    firebase.RemoteMessage remoteMessage) async {}

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
