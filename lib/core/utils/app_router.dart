import 'package:get/get.dart';
import 'package:salawati/features/athkar/presentation/screens/share_athkar.dart';
import 'package:salawati/features/auth/presentation/screens/auth_screen.dart';
import 'package:salawati/features/ibadah/presentation/screens/add_ibadah_screen.dart';
import 'package:salawati/features/layout/presentation/screens/layout_screen.dart';
import 'package:salawati/features/quran/presentation/screens/quran_screen.dart';
import 'package:salawati/features/quran/presentation/screens/reciters_screen.dart';
import 'package:salawati/features/settings/presentation/screens/about_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_calculation_higher_latitudes_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_calculation_juristic_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_calculation_methods_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_calculation_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_daylight_saving_screen.dart';
import 'package:salawati/features/settings/presentation/screens/calculation/settings_manual_calculation_screen.dart';
import 'package:salawati/features/settings/presentation/screens/notification/athan_sound_screen.dart';
import 'package:salawati/features/settings/presentation/screens/notification/notification_athan_screen.dart';
import 'package:salawati/features/settings/presentation/screens/notification/notification_athkar_screen.dart';
import 'package:salawati/features/settings/presentation/screens/notification/settings_notification_screen.dart';
import 'package:salawati/features/settings/presentation/screens/profile_screen.dart';
import 'package:salawati/features/settings/presentation/screens/settings_appearance_screen.dart';
import 'package:salawati/features/settings/presentation/screens/settings_language_screen.dart';
import 'package:salawati/features/settings/presentation/screens/settings_location_screen.dart';
import 'package:salawati/features/settings/presentation/screens/settings_screen.dart';
import 'package:salawati/features/permissions/presentation/screens/permission_flow_screen.dart';
import 'package:salawati/features/splash/presentation/screens/splash_screen.dart';
import 'package:salawati/features/tasbeeh/presentation/screens/add_tasbeeh_screen.dart';

mixin AppRouter {
  static const String kLayoutScreen = '/layoutScreen';
  static const String kSplashScreen = '/splashScreen';
  static const String kPermissionFlowScreen = '/permissionFlowScreen';
  static const String kSettingsScreen = '/settingsScreen';
  static const String kSettingsNotificationScreen =
      '/settingsNotificationScreen';
  static const String kNotificationAthkarScreen = '/notificationAthkarScreen';
  static const String kNotificationAthanScreen = '/notificationAthanScreen';
  static const String kSettingsAppearanceScreen = '/settingsAppearanceScreen';
  static const String kSettingsLanguageScreen = '/settingsLanguageScreen';
  static const String kSettingsAthanSoundScreen = '/settingsAthanSoundScreen';
  static const String kSettingsCalculationScreen = '/settingsCalculationScreen';
  static const String kSettingsCalculationMethodsScreen =
      '/settingsCalculationMethodsScreen';
  static const String kSettingsLocationScreen = '/settingsLocationScreen';
  static const String kSettingsCalculationJuristicScreen =
      '/settingsCalculationJuristicScreen';
  static const String kSettingsCalculationHigherLatitudesScreen =
      '/settingsCalculationHigherLatitudesScreen';
  static const String kSettingsDaylightSavingScreen =
      '/settingsDaylightSavingScreen';
  static const String kSettingsManualCalculationScreen =
      '/settingsManualCalculationScreen';
  static const String kQuranScreen = '/quranScreen';
  static const String kRecitersScreen = '/recitersScreen';
  static const String kZakahCalculatorScreen = '/zakahCalculatorScreen';
  static const String kZakahTypeScreen = '/zakahTypeScreen';
  static const String kAddIbadahScreen = '/addIbadahScreen';
  static const String kAuthScreen = '/authScreen';
  static const String kProfileScreen = '/profileScreen';
  static const String kAddTasbeehScreen = '/addTasbeehScreen';
  static const String kAboutUsScreen = '/aboutUs';
  static const String kShareThekerScreen = '/shareThekerScreen';

  static final List<GetPage> pages = [
    GetPage(
      transition: Transition.downToUp,
      name: kLayoutScreen,
      page: () => const LayoutScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSplashScreen,
      page: () => const SplashScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kPermissionFlowScreen,
      page: () => const PermissionFlowScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsScreen,
      page: () => const SettingsScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsAthanSoundScreen,
      page: () => const AthanSoundScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsNotificationScreen,
      page: () => const SettingsNotificationScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kNotificationAthkarScreen,
      page: () => const NotificationAthkarScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kNotificationAthanScreen,
      page: () => const NotificationAthanScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsAppearanceScreen,
      page: () => const SettingsApearanceScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsLanguageScreen,
      page: () => const SettingsLanguageScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsCalculationScreen,
      page: () => const SettingsCalculationScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsCalculationMethodsScreen,
      page: () => const SettingsCalculationMethodsScreen(),
    ),
    // GetPage(
    //   transition: Transition.downToUp,
    //   name: kSettingsLocationScreen,
    //   page: () => bloc.BlocProvider(
    //       create: (context) => SettingsCubit(),
    //       child: const SettingsLocationScreen()),
    // ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsLocationScreen,
      page: () => const SettingsLocationScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsCalculationJuristicScreen,
      page: () => const SettingsCalculationJuristicScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsCalculationHigherLatitudesScreen,
      page: () => const SettingsCalculationHigherLatitudesScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsDaylightSavingScreen,
      page: () => const SettingsDaylightSavingScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kSettingsManualCalculationScreen,
      page: () => const SettingsManualCalculationScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kRecitersScreen,
      page: () => const RecitersScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kQuranScreen,
      page: () => QuranScreen(reciter: Get.arguments),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kAddIbadahScreen,
      page: () => const AddIbadahScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kAuthScreen,
      page: () => AuthScreen(isSettings: Get.arguments),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kProfileScreen,
      page: () => const ProfileScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kAddTasbeehScreen,
      page: () => const AddTasbeehScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kAboutUsScreen,
      page: () => const AboutScreen(),
    ),
    GetPage(
      transition: Transition.downToUp,
      name: kShareThekerScreen,
      page: () => const ShareThekerScreen(),
    ),
  ];
}
