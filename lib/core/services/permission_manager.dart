import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:salawati/core/utils/app_notifications.dart';
import 'package:salawati/features/location/data/services/location_service.dart';

enum PermissionType {
  location,
  notification,
  batteryOptimization,
}

enum PermissionFlowStatus {
  notStarted,
  inProgress,
  completed,
  skipped,
}

class PermissionResult {
  final PermissionType type;
  final bool granted;
  final bool permanentlyDenied;
  final String? errorMessage;

  PermissionResult({
    required this.type,
    required this.granted,
    this.permanentlyDenied = false,
    this.errorMessage,
  });
}

class PermissionManager extends GetxController {
  static const String _permissionFlowKey = 'permission_flow_completed';
  static const String _locationPermissionKey = 'location_permission_asked';
  static const String _notificationPermissionKey = 'notification_permission_asked';
  static const String _batteryOptimizationKey = 'battery_optimization_asked';

  final GetStorage _storage = GetStorage();

  // Reactive state
  final RxBool _isPermissionFlowCompleted = false.obs;
  final Rx<PermissionFlowStatus> _flowStatus = PermissionFlowStatus.notStarted.obs;
  final RxMap<PermissionType, PermissionResult> _permissionResults =
      <PermissionType, PermissionResult>{}.obs;

  // Getters
  bool get isPermissionFlowCompleted => _isPermissionFlowCompleted.value;
  PermissionFlowStatus get flowStatus => _flowStatus.value;
  Map<PermissionType, PermissionResult> get permissionResults => _permissionResults;

  @override
  void onInit() {
    super.onInit();
    _loadPermissionState();
  }

  void _loadPermissionState() {
    _isPermissionFlowCompleted.value = _storage.read(_permissionFlowKey) ?? false;
    if (_isPermissionFlowCompleted.value) {
      _flowStatus.value = PermissionFlowStatus.completed;
    }
  }

  Future<bool> shouldShowPermissionFlow() async {
    // For testing purposes, always show permission flow
    // TODO: Remove this line and uncomment the logic below for production
    return true;

    // Check if permission flow was already completed
    // if (_isPermissionFlowCompleted.value) {
    //   return false;
    // }

    // Check if this is first time opening the app
    // final bool isFirstTime = !(_storage.hasData(_permissionFlowKey));
    // return isFirstTime;
  }

  Future<void> startPermissionFlow() async {
    _flowStatus.value = PermissionFlowStatus.inProgress;
  }

  Future<PermissionResult> requestLocationPermission() async {
    try {
      final locationService = Get.find<LocationService>();

      // Check current status first
      final currentStatus = await Permission.location.status;
      if (currentStatus.isGranted) {
        final result = PermissionResult(
          type: PermissionType.location,
          granted: true,
        );
        _permissionResults[PermissionType.location] = result;
        _storage.write(_locationPermissionKey, true);
        return result;
      }

      // Request permission through location service
      final permissionResult = await locationService.handlePermissionFlow();

      final bool granted = permissionResult == LocationPermissionResult.granted;
      final bool permanentlyDenied = permissionResult == LocationPermissionResult.permanentlyDenied;

      final result = PermissionResult(
        type: PermissionType.location,
        granted: granted,
        permanentlyDenied: permanentlyDenied,
      );

      _permissionResults[PermissionType.location] = result;
      _storage.write(_locationPermissionKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      final result = PermissionResult(
        type: PermissionType.location,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.location] = result;
      return result;
    }
  }

  Future<PermissionResult> requestNotificationPermission() async {
    try {
      // Check current status first
      final currentStatus = await Permission.notification.status;
      if (currentStatus.isGranted) {
        final result = PermissionResult(
          type: PermissionType.notification,
          granted: true,
        );
        _permissionResults[PermissionType.notification] = result;
        _storage.write(_notificationPermissionKey, true);
        return result;
      }

      // Request permission through app notifications
      final granted = await AppNotifications.checkAndRequestNotificationPermissions();

      final result = PermissionResult(
        type: PermissionType.notification,
        granted: granted,
        permanentlyDenied: !granted && currentStatus.isPermanentlyDenied,
      );

      _permissionResults[PermissionType.notification] = result;
      _storage.write(_notificationPermissionKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      final result = PermissionResult(
        type: PermissionType.notification,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.notification] = result;
      return result;
    }
  }

  Future<PermissionResult> requestBatteryOptimizationPermission() async {
    try {
      if (!Platform.isAndroid) {
        // iOS doesn't need battery optimization permission
        final result = PermissionResult(
          type: PermissionType.batteryOptimization,
          granted: true,
        );
        _permissionResults[PermissionType.batteryOptimization] = result;
        return result;
      }

      final status = await Permission.ignoreBatteryOptimizations.request();
      final granted = status.isGranted;

      final result = PermissionResult(
        type: PermissionType.batteryOptimization,
        granted: granted,
        permanentlyDenied: status.isPermanentlyDenied,
      );

      _permissionResults[PermissionType.batteryOptimization] = result;
      _storage.write(_batteryOptimizationKey, true);

      return result;
    } catch (e) {
      debugPrint('Error requesting battery optimization permission: $e');
      final result = PermissionResult(
        type: PermissionType.batteryOptimization,
        granted: false,
        errorMessage: e.toString(),
      );
      _permissionResults[PermissionType.batteryOptimization] = result;
      return result;
    }
  }

  void skipPermission(PermissionType type) {
    final result = PermissionResult(
      type: type,
      granted: false,
    );
    _permissionResults[type] = result;

    // Mark as asked so we don't show it again
    switch (type) {
      case PermissionType.location:
        _storage.write(_locationPermissionKey, true);
        break;
      case PermissionType.notification:
        _storage.write(_notificationPermissionKey, true);
        break;
      case PermissionType.batteryOptimization:
        _storage.write(_batteryOptimizationKey, true);
        break;
    }
  }

  void completePermissionFlow() {
    _flowStatus.value = PermissionFlowStatus.completed;
    _isPermissionFlowCompleted.value = true;
    _storage.write(_permissionFlowKey, true);
  }

  void skipPermissionFlow() {
    _flowStatus.value = PermissionFlowStatus.skipped;
    _isPermissionFlowCompleted.value = true;
    _storage.write(_permissionFlowKey, true);
  }

  // Helper methods to check individual permission status
  Future<bool> hasLocationPermission() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  Future<bool> hasNotificationPermission() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  Future<bool> hasBatteryOptimizationPermission() async {
    if (!Platform.isAndroid) return true;
    final status = await Permission.ignoreBatteryOptimizations.status;
    return status.isGranted;
  }

  // Reset permission flow (for testing or re-onboarding)
  void resetPermissionFlow() {
    _storage.remove(_permissionFlowKey);
    _storage.remove(_locationPermissionKey);
    _storage.remove(_notificationPermissionKey);
    _storage.remove(_batteryOptimizationKey);
    _isPermissionFlowCompleted.value = false;
    _flowStatus.value = PermissionFlowStatus.notStarted;
    _permissionResults.clear();
  }
}
